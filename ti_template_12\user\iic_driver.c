//#include "iic_driver.h"
///**
// * @brief IIC写一个字节数据
// * 
// * @param i2c                   iic句柄
// * @param I2C_TARGET_ADDRESS    目标设备地址
// * @param addr                  寄存器地址
// * @param data                  数据
// * @return int 
// */
//int I2C_WriteByte(I2C_Regs *i2c,uint32_t I2C_TARGET_ADDRESS,uint8_t addr,uint8_t data)
//{
//  uint8_t buff[2] = {0};
//  buff[0] = addr;
//  buff[1] = data;
//  /* Wait for I2C to be Idle */
//  while (!(DL_I2C_getControllerStatus(i2c) & DL_I2C_CONTROLLER_STATUS_IDLE));

//    /* Send the packet to the controller.
//    * This function will send Start + Stop automatically.
//    */
//		DL_I2C_fillControllerTXFIFO(i2c, &buff[0], 2);
//	
//    DL_I2C_startControllerTransfer(i2c, I2C_TARGET_ADDRESS,DL_I2C_CONTROLLER_DIRECTION_TX, 2);

//      /* Poll until the Controller writes all bytes */
//      while (DL_I2C_getControllerStatus(i2c) &DL_I2C_CONTROLLER_STATUS_BUSY_BUS);

//      /* Trap if there was an error */
//      if (DL_I2C_getControllerStatus(i2c) &DL_I2C_CONTROLLER_STATUS_ERROR) 
//      {
//          /* LED will remain high if there is an error */
//          __BKPT(0);
//      }
//    return 0;
//}
///**
// * @brief IIC写多个字节数据
// * 
// * @param i2c                   iic句柄
// * @param I2C_TARGET_ADDRESS    目标设备地址
// * @param data                  数据
// * @param length								数据长度
// * @return int 
// */
//int I2C_WriteMultiBytes(I2C_Regs *i2c, uint32_t I2C_TARGET_ADDRESS, uint8_t *data, uint8_t length)
//{
//    // 参数检查
//    if (!data || !length) {
//        return -1;
//    }
//    
//    /* 等待I2C空闲 */
//    while (!(DL_I2C_getControllerStatus(i2c) & DL_I2C_CONTROLLER_STATUS_IDLE));
//    
//    /* 填充TX FIFO */
//    DL_I2C_fillControllerTXFIFO(i2c, data, length);
//    
//    /* 启动传输 - 自动发送Start + Stop */
//    DL_I2C_startControllerTransfer(i2c, I2C_TARGET_ADDRESS, DL_I2C_CONTROLLER_DIRECTION_TX, length);
//    
//    /* 等待传输完成 */
//    while (DL_I2C_getControllerStatus(i2c) & DL_I2C_CONTROLLER_STATUS_BUSY_BUS);
//    
//    /* 检查错误 */
//    if (DL_I2C_getControllerStatus(i2c) & DL_I2C_CONTROLLER_STATUS_ERROR) {
//        /* 有错误发生 */
//        return -1;
//    }
//    
//    return 0; // 成功
//}
///**
// * @brief IIC数据读取
// * 
// * @param i2c        IIC句柄
// * @param slave_addr 设备地址
// * @param reg_addr   寄存器地址
// * @param length     读取长度
// * @param data       数据存储地址
// * @return int 
// */
//int IIC_Read(I2C_Regs *i2c,unsigned char slave_addr,unsigned char reg_addr,unsigned char length,unsigned char *data)
//{
//	
//    unsigned i = 0; //记录已读字节数索引
//    //如果请求读取长度为0,直接返回
//    if (!length) return 0;
//	
//		if(reg_addr != 0x00)
//		
//		//向IIC控制器发送要读取的寄存器地址
//		DL_I2C_transmitControllerData(i2c, reg_addr);
//		//配置控制器:当TX FIFO为空时，自动切换到读取模式
//		i2c->MASTER.MCTR = I2C_MCTR_RD_ON_TXEMPTY_ENABLE;
//		//清除之前的RX完成中断标志
//		DL_I2C_clearInterruptStatus(i2c, DL_I2C_INTERRUPT_CONTROLLER_RX_DONE);
//		//等待IIC总线空闲,确保写入地址阶段完成
//		while (!(DL_I2C_getControllerStatus(i2c) & DL_I2C_CONTROLLER_STATUS_IDLE));
//		

//    //启动IIC读取传输，读取指定长度的数据
//    DL_I2C_startControllerTransfer(i2c, slave_addr, DL_I2C_CONTROLLER_DIRECTION_RX, length);
//	
//   do 
//	{
//        //如果RX FIFO中有数据
//        if(!DL_I2C_isControllerRXFIFOEmpty(i2c))
//        {
//            uint8_t c;
//            //从RX FIFO中读取一个字节
//            c = DL_I2C_receiveControllerData(i2c);
//            if (i < length)
//            {
//                //保存到用户提供的缓冲区
//                data[i] = c;
//                //递增已读计数
//                ++i;
//            }
//        }
//    } 
//    //循环接收直到RX完成中断
//		while(!DL_I2C_getRawInterruptStatus(i2c, DL_I2C_INTERRUPT_CONTROLLER_RX_DONE));
//    //中断触发后,检查FIFO中是否还有剩余数据,并读取
//    if (!DL_I2C_isControllerRXFIFOEmpty(i2c))
//    {
//        uint8_t c;
//        c = DL_I2C_receiveControllerData(i2c);
//        if (i < length)
//        {
//            data[i] = c;
//            ++i;
//        }
//    }
//    //清除控制器模式寄存器,恢复默认状态
//    i2c->MASTER.MCTR = 0;
//    //清空TX FIFO准备下次使用
//    DL_I2C_flushControllerTXFIFO(i2c);
//    //如果读取字节数与请求长度一致
//    if(i == length)
//		{
//			return 0;//返回成功
//		} 
//    else
//        return -1;//否则返回失败
//}
///**
// * @brief BNO080专用IIC数据读取函数
// * 
// * @param i2c        IIC句柄
// * @param slave_addr 设备地址
// * @param length     读取长度
// * @param data       数据存储地址
// * @return int       0:成功, -1:失败
// */
//int BNO080_IIC_Read(I2C_Regs *i2c, unsigned char slave_addr, unsigned char length, unsigned char *data)
//{
//    unsigned i = 0; // 记录已读字节数索引
//    
//    // 如果请求读取长度为0,直接返回
//    if (!length) return 0;
//    
//    // 清除之前的RX完成中断标志
//    DL_I2C_clearInterruptStatus(i2c, DL_I2C_INTERRUPT_CONTROLLER_RX_DONE);
//    
//    // 直接启动IIC读取传输，不写入寄存器地址
//    DL_I2C_startControllerTransfer(i2c, slave_addr, DL_I2C_CONTROLLER_DIRECTION_RX, length);
//    
//    do 
//    {
//        // 如果RX FIFO中有数据
//        if(!DL_I2C_isControllerRXFIFOEmpty(i2c))
//        {
//            uint8_t c;
//            // 从RX FIFO中读取一个字节
//            c = DL_I2C_receiveControllerData(i2c);
//            if (i < length)
//            {
//                // 保存到用户提供的缓冲区
//                data[i] = c;
//                // 递增已读计数
//                ++i;
//            }
//        }
//    } 
//    // 循环接收直到RX完成中断
//    while(!DL_I2C_getRawInterruptStatus(i2c, DL_I2C_INTERRUPT_CONTROLLER_RX_DONE));
//    
//    // 中断触发后,检查FIFO中是否还有剩余数据,并读取
//    while (!DL_I2C_isControllerRXFIFOEmpty(i2c) && i < length)
//    {
//        uint8_t c;
//        c = DL_I2C_receiveControllerData(i2c);
//        data[i] = c;
//        ++i;
//    }
//    
//    // 清空TX FIFO准备下次使用
//    DL_I2C_flushControllerTXFIFO(i2c);
//    
//    // 如果读取字节数与请求长度一致
//    if(i == length)
//    {
//        return 0; // 返回成功
//    } 
//    else
//        return -1; // 否则返回失败
//}
//void ShortToChar(short sData,unsigned char cData[])
//{
//	cData[0]=sData&0xff;
//	cData[1]=sData>>8;
//}
//short CharToShort(unsigned char cData[])
//{
//	return ((short)cData[1]<<8)|cData[0];
//}
//unsigned char chrTemp[30];
//unsigned char str[100];
//float a[3],w[3],h[3],Angle[3];
//void iic_test(void)
//{
//		
//		IIC_Read(IMU_INST,0x50, 0x34, 24,&chrTemp[0]);
//		a[0] = (float)CharToShort(&chrTemp[0])/32768*16;
//		a[1] = (float)CharToShort(&chrTemp[2])/32768*16;
//		a[2] = (float)CharToShort(&chrTemp[4])/32768*16;
//		w[0] = (float)CharToShort(&chrTemp[6])/32768*2000;
//		w[1] = (float)CharToShort(&chrTemp[8])/32768*2000;
//		w[2] = (float)CharToShort(&chrTemp[10])/32768*2000;
//		h[0] = CharToShort(&chrTemp[12]);
//		h[1] = CharToShort(&chrTemp[14]);
//		h[2] = CharToShort(&chrTemp[16]);
//		Angle[0] = (float)CharToShort(&chrTemp[18])/32768*180;
//		Angle[1] = (float)CharToShort(&chrTemp[20])/32768*180;
//		Angle[2] = (float)CharToShort(&chrTemp[22])/32768*180;
//		
//		my_printf(UART_0_INST,"0x50:  a:%.3f %.3f %.3f w:%.3f %.3f %.3f  h:%.0f %.0f %.0f  Angle:%.3f %.3f %.3f \r\n",
//		a[0],a[1],a[2],w[0],w[1],w[2],h[0],h[1],h[2],Angle[0],Angle[1],Angle[2]);
//}
