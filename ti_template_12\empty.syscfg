/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.1+4189"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const GPIO7   = GPIO.addInstance();
const GPIO8   = GPIO.addInstance();
const GPIO9   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 4;

const mux4       = system.clockTree["EXHFMUX"];
mux4.inputSelect = "EXHFMUX_XTAL";

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

const mux12       = system.clockTree["SYSPLLMUX"];
mux12.inputSelect = "zSYSPLLMUX_HFCLK";

const pinFunction3     = system.clockTree["HFCLKEXT"];
pinFunction3.inputFreq = 40;

const pinFunction4        = system.clockTree["HFXT"];
pinFunction4.enable       = true;
pinFunction4.inputFreq    = 40;
pinFunction4.HFXTStartup  = 100;
pinFunction4.HFCLKMonitor = true;

GPIO1.$name                              = "LED1";
GPIO1.port                               = "PORTB";
GPIO1.associatedPins[0].$name            = "PIN_22";
GPIO1.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO1.associatedPins[0].assignedPin      = "22";
GPIO1.associatedPins[0].initialValue     = "SET";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                              = "KEY";
GPIO2.port                               = "PORTB";
GPIO2.associatedPins[0].$name            = "PIN_21";
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].assignedPin      = "21";

GPIO3.$name                              = "ENCODER";
GPIO3.port                               = "PORTB";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name            = "left_a";
GPIO3.associatedPins[0].direction        = "INPUT";
GPIO3.associatedPins[0].internalResistor = "PULL_DOWN";
GPIO3.associatedPins[0].interruptEn      = true;
GPIO3.associatedPins[0].polarity         = "RISE";
GPIO3.associatedPins[0].pin.$assign      = "PB6";
GPIO3.associatedPins[1].$name            = "left_b";
GPIO3.associatedPins[1].direction        = "INPUT";
GPIO3.associatedPins[1].internalResistor = "PULL_UP";
GPIO3.associatedPins[1].interruptEn      = true;
GPIO3.associatedPins[1].polarity         = "FALL";
GPIO3.associatedPins[1].pin.$assign      = "PB7";
GPIO3.associatedPins[2].$name            = "right_a";
GPIO3.associatedPins[2].direction        = "INPUT";
GPIO3.associatedPins[2].interruptEn      = true;
GPIO3.associatedPins[2].polarity         = "RISE";
GPIO3.associatedPins[2].internalResistor = "PULL_DOWN";
GPIO3.associatedPins[2].pin.$assign      = "PB8";
GPIO3.associatedPins[3].$name            = "right_b";
GPIO3.associatedPins[3].direction        = "INPUT";
GPIO3.associatedPins[3].internalResistor = "PULL_UP";
GPIO3.associatedPins[3].interruptEn      = true;
GPIO3.associatedPins[3].polarity         = "FALL";
GPIO3.associatedPins[3].pin.$assign      = "PB9";

GPIO4.$name                         = "BNO_RST";
GPIO4.associatedPins[0].$name       = "PIN_4";
GPIO4.associatedPins[0].pin.$assign = "PB14";

GPIO5.$name                         = "MOTOR_L";
GPIO5.port                          = "PORTB";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name       = "AIN1";
GPIO5.associatedPins[0].pin.$assign = "PB0";
GPIO5.associatedPins[1].$name       = "AIN2";
GPIO5.associatedPins[1].pin.$assign = "PB1";

GPIO6.$name                         = "MOTOR_R";
GPIO6.port                          = "PORTB";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].$name       = "BIN1";
GPIO6.associatedPins[0].pin.$assign = "PB4";
GPIO6.associatedPins[1].$name       = "BIN2";
GPIO6.associatedPins[1].pin.$assign = "PB5";

GPIO7.port                           = "PORTB";
GPIO7.$name                          = "MOTOR_EN";
GPIO7.associatedPins[0].$name        = "STBY";
GPIO7.associatedPins[0].initialValue = "SET";
GPIO7.associatedPins[0].pin.$assign  = "PB12";

GPIO8.$name                              = "GRAY";
GPIO8.port                               = "PORTB";
GPIO8.associatedPins.create(7);
GPIO8.associatedPins[0].$name            = "G_0";
GPIO8.associatedPins[0].direction        = "INPUT";
GPIO8.associatedPins[0].internalResistor = "PULL_UP";
GPIO8.associatedPins[0].pin.$assign      = "PB17";
GPIO8.associatedPins[1].$name            = "G_1";
GPIO8.associatedPins[1].direction        = "INPUT";
GPIO8.associatedPins[1].internalResistor = "PULL_UP";
GPIO8.associatedPins[1].pin.$assign      = "PB18";
GPIO8.associatedPins[2].$name            = "G_2";
GPIO8.associatedPins[2].direction        = "INPUT";
GPIO8.associatedPins[2].internalResistor = "PULL_UP";
GPIO8.associatedPins[2].pin.$assign      = "PB19";
GPIO8.associatedPins[3].$name            = "G_3";
GPIO8.associatedPins[3].direction        = "INPUT";
GPIO8.associatedPins[3].internalResistor = "PULL_UP";
GPIO8.associatedPins[3].pin.$assign      = "PB20";
GPIO8.associatedPins[4].$name            = "G_4";
GPIO8.associatedPins[4].direction        = "INPUT";
GPIO8.associatedPins[4].internalResistor = "PULL_UP";
GPIO8.associatedPins[4].pin.$assign      = "PB24";
GPIO8.associatedPins[5].$name            = "G_5";
GPIO8.associatedPins[5].direction        = "INPUT";
GPIO8.associatedPins[5].internalResistor = "PULL_UP";
GPIO8.associatedPins[5].pin.$assign      = "PB25";
GPIO8.associatedPins[6].$name            = "G_6";
GPIO8.associatedPins[6].direction        = "INPUT";
GPIO8.associatedPins[6].internalResistor = "PULL_UP";
GPIO8.associatedPins[6].pin.$assign      = "PB2";

GPIO9.$name                              = "I2C_PINS";
GPIO9.port                               = "PORTA";
GPIO9.associatedPins.create(2);
GPIO9.associatedPins[0].$name            = "SCL";
GPIO9.associatedPins[0].direction        = "OUTPUT";
GPIO9.associatedPins[0].initialValue     = "SET";
GPIO9.associatedPins[0].pin.$assign      = "PA0";
GPIO9.associatedPins[1].$name            = "SDA";
GPIO9.associatedPins[1].direction        = "OUTPUT";
GPIO9.associatedPins[1].initialValue     = "SET";
GPIO9.associatedPins[1].pin.$assign      = "PA1";

PWM1.timerStartTimer                    = true;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.clockPrescale                      = 2;
PWM1.$name                              = "MOTOR_PWM";
PWM1.peripheral.$assign                 = "TIMG0";
PWM1.peripheral.ccp0Pin.$assign         = "PB10";
PWM1.peripheral.ccp1Pin.$assign         = "PB11";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric13";

SYSCTL.clockTreeEn           = true;
SYSCTL.forceDefaultClkConfig = true;
SYSCTL.validateClkStatus     = true;

SYSTICK.periodEnable      = true;
SYSTICK.period            = 80000;
SYSTICK.interruptEnable   = true;
SYSTICK.interruptPriority = "0";
SYSTICK.systickEnable     = true;

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.uartClkSrc               = "MFCLK";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                    = "UART_1";
UART2.uartClkSrc               = "MFCLK";
UART2.targetBaudRate           = 115200;
UART2.enabledInterrupts        = ["RX"];
UART2.peripheral.$assign       = "UART1";
UART2.peripheral.rxPin.$assign = "PA9";
UART2.peripheral.txPin.$assign = "PA8";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
pinFunction4.peripheral.$suggestSolution           = "SYSCTL";
pinFunction4.peripheral.hfxInPin.$suggestSolution  = "PA5";
pinFunction4.peripheral.hfxOutPin.$suggestSolution = "PA6";
GPIO1.associatedPins[0].pin.$suggestSolution       = "PB22";
Board.peripheral.$suggestSolution                  = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution         = "PA20";
Board.peripheral.swdioPin.$suggestSolution         = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution       = "PB21";
