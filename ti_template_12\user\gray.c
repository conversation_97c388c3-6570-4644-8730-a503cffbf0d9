#include "gray.h"

uint16_t Digtal_12; // 12路灰度传感器数据
// 12路灰度传感器权重数组 - 对称设计，边缘权重小，中心权重大
float gray_weights_12[12] = {
    -11.0f,  // bit0 - 最左边缘
    -9.0f,   // bit1 - 左边缘
    -7.0f,   // bit2 - 左外侧
    -5.0f,   // bit3 - 左内侧
    -3.0f,   // bit4 - 左中
    -1.0f,   // bit5 - 左中心
    1.0f,    // bit6 - 右中心
    3.0f,    // bit7 - 右中
    5.0f,    // bit8 - 右内侧
    7.0f,    // bit9 - 右外侧
    9.0f,    // bit10 - 右边缘
    11.0f    // bit11 - 最右边缘
};

float g_line_position_error; // 循迹偏差值
extern unsigned char L_count;
unsigned char baochi_flag=0;
uint16_t Digtal_Get_12(void)
{
	static uint16_t backup_data = 0x0000;  // 备份数据，用于I2C通信失败时
	static uint8_t error_count = 0;        // 错误计数器

	// 通过I2C读取12路灰度传感器数据
	uint16_t raw_12bit_data = pca9555_read_bit12(0x40);

	// 数据有效性检查
	if(raw_12bit_data == 0x0000 || raw_12bit_data == 0x0FFF) {
		error_count++;
		if(error_count > 3) {
			// 连续错误超过3次，使用备份数据
			return backup_data;
		}
		// 重试读取
		raw_12bit_data = pca9555_read_bit12(0x40);
		if(raw_12bit_data == 0x0000 || raw_12bit_data == 0x0FFF) {
			return backup_data;
		}
	}

	// 数据有效，重置错误计数
	error_count = 0;

	// 直接返回12路数据，取低12位
	uint16_t valid_12bit = raw_12bit_data & 0x0FFF;

	// 更新备份数据
	backup_data = valid_12bit;

	return valid_12bit;
}

void Gray_Task(void)
{
	// 获取12路灰度传感器数据
	Digtal_12 = ~Digtal_Get_12();  // 取反：1表示检测到黑线，0表示白色

    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    // 遍历12路传感器数据
    for(uint8_t i = 0; i < 12; i++)
    {
      if((Digtal_12 >> i) & 0x01)  // 检查第i位是否为1（检测到黑线）
      {
        weighted_sum += gray_weights_12[i];  // 直接使用对应权重
        black_line_count++;
      }
    }

    // 计算加权平均偏差
    if(black_line_count > 0)
      g_line_position_error = weighted_sum / (float)black_line_count;
	else
		g_line_position_error = 0;

	// 左转检测逻辑 - 使用12路传感器的右侧3路检测
	// 检测bit9、bit10、bit11同时为1（右侧连续3路检测到黑线）
	if(pid_running && ((Digtal_12>>9)&0x01) && ((Digtal_12>>10)&0x01) && ((Digtal_12>>11)&0x01))
  {
//	  if(baochi_flag==0)
//		 L_count++;
//	  baochi_flag=1;
//	  g_line_position_error=-15;
	  pid_running=0;
	  Motor_Stop();

	  if(++L_count==3)
		  return;
	  Motor_Speed_l(-20);
	  Motor_Speed_r(35);
	  delay_ms(200);
	  pid_running=1;
	  basic_speed=45;
	  pid_reset(&pid_speed_left);
	  pid_reset(&pid_speed_right);
	  pid_reset(&pid_line_gray);
	  pid_set_target(&pid_speed_left, basic_speed);
	  pid_set_target(&pid_speed_right, basic_speed);
//	  pid_set_target(&pid_line_gray, 0);
  }
  else
  {
	  baochi_flag=0;
  }
//  my_printf(UART_0_INST,"count:%d\r\n",L_count);
//  	my_printf(UART_0_INST,"error:%.2f Digtal_12: %04X\r\n",g_line_position_error, Digtal_12);

}


