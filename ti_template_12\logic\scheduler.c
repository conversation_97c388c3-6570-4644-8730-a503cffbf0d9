#include "scheduler.h"

uint32_t uwTick;    //系统时间
uint8_t task_num;
extern unsigned char Digtal;
unsigned char L_count=0;

//任务结构体
typedef struct{
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
}task_t;
unsigned char output_ff_flag;
unsigned int intput_timer500ms;

void system_Task(void)
{
//	if(L_count==2)
//	{
//		pid_running=0;
//		Motor_Stop();
//	}
}
//静态任务列表
static task_t scheduler_task[] = {
		{uart0_task,5,0},
//		{uart1_task,6,0},
//		{uart2_task,7,0},
	{Gray_Task,10,0},
    {key_task,10,0},
    {encoder_task,10,0}, //编码器运行周期更改时要同步修改SAMPLE_TIME
	{PID_Task,10,0},
	{system_Task,4,0}
};

/**
 * @brief 调度器初始化
 * @param none
 * @return none
 */
void scheduler_init(void)
{
    // 计算任务数组的元素个数，并将结果存储在 task_num 中
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}
/**
 * @brief 调度器运行函数
 * @param none
 * @return none
 */
void scheduler_run(void)
{
    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前的系统时间（毫秒）
        uint32_t now_time = uwTick;

        // 检查当前时间是否达到任务的执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 更新任务的上次运行时间为当前时间
            scheduler_task[i].last_run = now_time;
            // 执行任务函数
            scheduler_task[i].task_func();
        }
    }
}

/**
 * @brief 滴答定时器中断服务函数
 * @param none
 * @return none
 */
void SysTick_Handler(void)
{
    uwTick++;
}
/**
 * @brief DL库延时函数(ms)
 * @param none
 * @return none
 */
void DL_Delay(uint32_t delayMs)
{
    uint32_t startTick = uwTick;
    
    // 等待指定的毫秒数
    while ((uwTick - startTick) < delayMs) {
        // 可以在这里添加低功耗模式
        // __WFI(); // 等待中断，降低功耗
    }
}