#include "bsp_system.h"

/**
 * @brief 用户配置函数声明
 */
void user_config(void);
/**
 * @brief 主函数
 * 
 * @param none
 * 
 * @return int 
 */
int main(void)
{
	//系统配置
    SYSCFG_DL_init();
	my_printf(UART_0_INST, "test\r\n");
	//用户配置
    user_config();
	//编码器初始化
//	Motor_Init();
	encoder_config();
	PID_Init();
	//调度器初始化
    scheduler_init();
//	Motor_Speed_l(30);
//	Motor_Speed_r(30);
    while (1)
    {

		
//		DL_TimerG_setCaptureCompareValue(MOTOR_PWM_INST,50,GPIO_MOTOR_PWM_C0_IDX);
		scheduler_run();
    
		}
}
/**
 * @brief 用户配置项
 * 
 * @param none
 * 
 * @return none
 */
void user_config(void)
{
    #ifdef UART_0_INST_INT_IRQN
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
    #endif

    #ifdef UART_1_INST_INT_IRQN
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);
    #endif
		
		#ifdef ENCODER_INT_IRQN
		//开启编码器中断
		NVIC_EnableIRQ(ENCODER_INT_IRQN);
		#endif

		//I2C初始化和设备检测
		i2c_CheckDevice(0x40);  //检测PCA9555设备
		pca9555_config_input(); //配置PCA9555为输入模式

}
