#ifndef __UART_DRIVER_H
#define __UART_DRIVER_H

#include "bsp_system.h"

/**
 * @brief 串口输出重定向
 * 
 * @param uart 		UART_0_INST
 *                  UART_1_INST
 *                  UART_2_INST
 *                  UART_3_INST
 * 
 * @param format 	自定义字符串
 * 
 * @param ... 		变量
 * 
 * @return int 		
 */
int my_printf(UART_Regs *uart, const char *format, ...);
/**
 * @brief 串口任务函数(weak)
 *        具体解析逻辑,需用户自行编写
 * @param none
 * 
 * @return none 
 */
void uart0_task(void);
void uart1_task(void);
void uart2_task(void);
void uart3_task(void);

#endif