#include "gray.h"

// 测试用的权重数组（与实际相同）
extern float gray_weights[7];
extern float g_line_position_error;

/**
 * @brief 模拟12路数据并测试映射和权重计算
 * @param raw_12bit 12路原始数据
 * @return 计算得到的偏差值
 */
float test_gray_mapping_and_weight(uint16_t raw_12bit)
{
    // 模拟Digtal_Get()的映射逻辑
    unsigned char mapped_7bit = (raw_12bit >> 3) & 0x7F;
    unsigned char Digtal_test = ~mapped_7bit;  // 模拟取反操作
    
    // 模拟Gray_Task()的权重计算逻辑
    float weighted_sum = 0;
    uint8_t black_line_count = 0;
    
    for(uint8_t i = 0; i < 7; i++)
    {
        if((Digtal_test>>i) & 0x01)
        {
            weighted_sum += gray_weights[6-i];
            black_line_count++;
        }
    }
    
    float position_error = 0;
    if(black_line_count > 0)
        position_error = weighted_sum / (float)black_line_count;
    
    return position_error;
}

/**
 * @brief 测试左转检测逻辑
 * @param raw_12bit 12路原始数据
 * @return 是否检测到左转条件
 */
bool test_left_turn_detection(uint16_t raw_12bit)
{
    // 模拟数据映射和取反
    unsigned char mapped_7bit = (raw_12bit >> 3) & 0x7F;
    unsigned char Digtal_test = ~mapped_7bit;
    
    // 测试左转检测逻辑：bit6、bit5、bit4同时为1
    return ((Digtal_test>>6)&0x01) && ((Digtal_test>>5)&0x01) && ((Digtal_test>>4)&0x01);
}

/**
 * @brief 运行所有测试用例
 */
void run_gray_algorithm_tests(void)
{
    // 测试用例1: 中心线检测
    // 12路数据: bit5和bit6为1 (对应7路的bit2和bit3)
    uint16_t test_center = 0b000001100000;  // 0x0060
    float error_center = test_gray_mapping_and_weight(test_center);
    // 预期结果: 权重-2.0和0.0的平均值 = -1.0
    
    // 测试用例2: 左偏检测
    // 12路数据: bit3和bit4为1 (对应7路的bit0和bit1)
    uint16_t test_left = 0b000000011000;   // 0x0018
    float error_left = test_gray_mapping_and_weight(test_left);
    // 预期结果: 权重-6.0和-4.5的平均值 = -5.25
    
    // 测试用例3: 右偏检测
    // 12路数据: bit8和bit9为1 (对应7路的bit5和bit6)
    uint16_t test_right = 0b001100000000;  // 0x0300
    float error_right = test_gray_mapping_and_weight(test_right);
    // 预期结果: 权重4.5和6.0的平均值 = 5.25
    
    // 测试用例4: 左转检测
    // 12路数据: bit7、bit8、bit9为1 (对应7路的bit4、bit5、bit6)
    uint16_t test_left_turn = 0b001110000000;  // 0x0380
    bool is_left_turn = test_left_turn_detection(test_left_turn);
    // 预期结果: true
    
    // 测试用例5: 单点检测
    // 12路数据: 仅bit6为1 (对应7路的bit3)
    uint16_t test_single = 0b000001000000;  // 0x0040
    float error_single = test_gray_mapping_and_weight(test_single);
    // 预期结果: 权重0.0
    
    // 可以通过串口输出测试结果进行验证
    // my_printf(UART_0_INST, "Center Error: %.2f (Expected: -1.0)\r\n", error_center);
    // my_printf(UART_0_INST, "Left Error: %.2f (Expected: -5.25)\r\n", error_left);
    // my_printf(UART_0_INST, "Right Error: %.2f (Expected: 5.25)\r\n", error_right);
    // my_printf(UART_0_INST, "Left Turn: %d (Expected: 1)\r\n", is_left_turn);
    // my_printf(UART_0_INST, "Single Error: %.2f (Expected: 0.0)\r\n", error_single);
}

/**
 * @brief 验证权重数组的对称性
 */
void verify_weight_symmetry(void)
{
    // 验证权重数组的对称性
    // gray_weights[7] = {-6.0f, -4.5, -2.0f, 0.0f , 2.0f, 4.5f, 6.0f}
    //                    [0]    [1]   [2]    [3]    [4]   [5]   [6]
    
    // 检查对称性: weights[0] + weights[6] = 0, weights[1] + weights[5] = 0, etc.
    bool symmetry_ok = true;
    
    for(int i = 0; i < 3; i++) {
        if(gray_weights[i] + gray_weights[6-i] != 0.0f) {
            symmetry_ok = false;
            break;
        }
    }
    
    // 中心权重应该为0
    if(gray_weights[3] != 0.0f) {
        symmetry_ok = false;
    }
    
    // 可以通过串口输出验证结果
    // my_printf(UART_0_INST, "Weight Symmetry: %s\r\n", symmetry_ok ? "PASS" : "FAIL");
}

/**
 * @brief 测试边界条件
 */
void test_boundary_conditions(void)
{
    // 测试全0数据（无传感器触发）
    uint16_t test_all_zero = 0b000000000000;
    float error_zero = test_gray_mapping_and_weight(test_all_zero);
    // 预期结果: 0.0 (无黑线检测)
    
    // 测试全1数据（所有传感器触发）
    uint16_t test_all_one = 0b111111111111;
    float error_all = test_gray_mapping_and_weight(test_all_one);
    // 预期结果: 0.0 (所有权重的平均值)
    
    // 测试最左边界
    uint16_t test_leftmost = 0b000000001000;  // bit3
    float error_leftmost = test_gray_mapping_and_weight(test_leftmost);
    // 预期结果: -6.0
    
    // 测试最右边界
    uint16_t test_rightmost = 0b001000000000;  // bit9
    float error_rightmost = test_gray_mapping_and_weight(test_rightmost);
    // 预期结果: 6.0
}
