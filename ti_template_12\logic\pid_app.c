#include "pid_app.h"


//#define basic_speed 80

/* PID ������ʵ�� */
PID_T pid_speed_left;  // �����ٶȻ�
PID_T pid_speed_right; // �����ٶȻ�

PID_T pid_line_gray;
extern float g_line_position_error; // ѭ�����ֵ
extern encoder encoder_left;//B6-E1B,B7-E1A
extern encoder encoder_right;//B8-E2A,B9-E2B,
// ����ʽPID��P-�ȶ��ԣ�I-��Ӧ�ԣ�D-׼ȷ��
int basic_speed=45;

PidParams_t pid_params_left = {
    .kp = 1.0f,        
    .ki = 0.05f,      
    .kd = 0.6f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_right = {
    .kp = 1.0f,        
    .ki = 0.05f,      
    .kd = 0.6f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_line = {
    .kp = 3.0f,      // 降低比例系数，因为12路权重范围更大
    .ki = 0.0f,
    .kd = 10.0f,     // 适当降低微分系数
    .out_min = -999.0f,
    .out_max = 999.0f,
};

void PID_Init(void)
{
//	    
//	Motor_Speed_l(30);
//    Motor_Speed_r(30);
//	delay_ms(100);
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);
  pid_init(&pid_line_gray,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
  pid_set_target(&pid_speed_left, basic_speed);
  pid_set_target(&pid_speed_right, basic_speed);
  pid_set_target(&pid_line_gray, 0);
}
bool pid_running = false; // PID ����ʹ�ܿ���
void Line_PID_control(void) // ѭ��������
{
  int line_pid_output = 0;
  // ʹ��λ��ʽ PID ��������ѭ�����������
  line_pid_output = pid_calculate_positional(&pid_line_gray, g_line_position_error); 
  // ����޷�
  line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
  // ����ֵ�������ٶȻ���Ŀ������
  pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
  pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
}
void PID_Task(void)
{
    if(pid_running == false) return;
basic_speed=45;
    float output_left, output_right;
	Line_PID_control();
	output_left = pid_calculate_incremental(&pid_speed_left, encoder_left.speed_cm_s);
    output_right = pid_calculate_incremental(&pid_speed_right, encoder_right.speed_cm_s);
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
//	my_printf(UART_0_INST,"{left_filtered}%.2f,%.2f\r\n", pid_speed_left.target, encoder_left.speed_cm_s);
//	my_printf(UART_0_INST,"{right_filtered}%.2f,%.2f\r\n", pid_speed_right.target, encoder_right.speed_cm_s);
    // ���õ���ٶ�
    Motor_Speed_l(output_left);
    Motor_Speed_r(output_right);
}