T83E0 000:238.320   SEGGER J-Link V8.54 Log File
T83E0 000:238.716   DLL Compiled: Jul 23 2025 12:15:33
T83E0 000:238.729   Logging started @ 2025-07-31 11:50
T83E0 000:238.740   Process: C:\Keil_v5\UV4\UV4.exe
T83E0 000:239.014 - 238.751ms 
T83E0 000:239.196 JLINK_SetWarnOutHandler(...)
T83E0 000:239.217 - 0.172ms 
T83E0 000:239.236 JLINK_OpenEx(...)
T83E0 000:244.264   Firmware: J-Link OB-STM32F072-128KB-CortexM compiled Oct 30 2023 12:11:14
T83E0 000:245.538   Firmware: J-Link OB-STM32F072-128KB-CortexM compiled Oct 30 2023 12:11:14
T83E0 000:245.823   Decompressing FW timestamp took 166 us
T83E0 000:261.294   Hardware: V1.00
T83E0 000:261.320   S/N: 4294967295
T83E0 000:261.336   OEM: SEGGER
T83E0 000:261.353   Feature(s): GDB, FlashDL, FlashBP, JFlash
T83E0 000:262.415   Bootloader: (FW returned invalid version)
T83E0 000:262.922   USB speed mode: Full speed (12 MBit/s)
T83E0 000:263.344   TELNET listener socket opened on port 19021
T83E0 000:263.619   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T83E0 000:263.719   WEBSRV Webserver running on local port 19080
T83E0 000:263.859   Looking for J-Link GUI Server exe at: C:\Keil_v5\ARM\Segger\JLinkGUIServer.exe
T83E0 000:264.002   Looking for J-Link GUI Server exe at: C:\Program Files\SEGGER\JLink_V854\JLinkGUIServer.exe
T83E0 000:264.061   Forking J-Link GUI Server: C:\Program Files\SEGGER\JLink_V854\JLinkGUIServer.exe
T83E0 000:384.650   J-Link GUI Server info: "J-Link GUI server V8.54 "
T83E0 000:389.181 - 149.793ms returns "O.K."
T83E0 000:389.216 JLINK_GetEmuCaps()
T83E0 000:389.230 - 0.012ms returns 0xB8EA5A33
T83E0 000:389.245 JLINK_TIF_GetAvailable(...)
T83E0 000:389.389 - 0.144ms 
T83E0 000:389.414 JLINK_SetErrorOutHandler(...)
T83E0 000:389.426 - 0.011ms 
T83E0 000:389.694 JLINK_ExecCommand("ProjectFile = "D:\msp\1_ti_template(1)\ti_template\keil\JLinkSettings.ini"", ...). 
T83E0 000:409.420   Ref file found at: C:\Keil_v5\ARM\Segger\JLinkDevices.ref
T83E0 000:409.589   REF file references invalid XML file: C:\Program Files\SEGGER\JLink_V854\JLinkDevices.xml
T83E0 000:411.160   Flash bank @ 0x00000000: SFL: Parsing sectorization info from ELF file
T83E0 000:411.192     FlashDevice.SectorInfo[0]: .SectorSize = 0x00000400, .SectorStartAddr = 0x00000000
T83E0 000:411.338   Device "PAC5210" selected.
T83E0 000:411.656 - 21.964ms returns 0x00
T83E0 000:411.675 JLINK_ExecCommand("Device = MSPM0G3507", ...). 
T83E0 000:412.071   Flash bank @ 0x00000000: SFL: Parsing sectorization info from ELF file
T83E0 000:412.093     FlashDevice.SectorInfo[0]: .SectorSize = 0x00000400, .SectorStartAddr = 0x00000000
T83E0 000:412.118   Device "PAC5210" selected.
T83E0 000:412.401 - 0.713ms returns 0x00
T83E0 000:412.418 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T83E0 000:412.537   ERROR: Unknown command
T83E0 000:412.562 - 0.131ms returns 0x01
T83E0 000:412.580 JLINK_GetHardwareVersion()
T83E0 000:412.595 - 0.015ms returns 10000
T83E0 000:412.611 JLINK_GetDLLVersion()
T83E0 000:412.626 - 0.014ms returns 85400
T83E0 000:412.641 JLINK_GetOEMString(...)
T83E0 000:412.660 JLINK_GetFirmwareString(...)
T83E0 000:412.675 - 0.014ms 
T83E0 000:412.696 JLINK_GetDLLVersion()
T83E0 000:412.713 - 0.016ms returns 85400
T83E0 000:412.730 JLINK_GetCompileDateTime()
T83E0 000:412.745 - 0.015ms 
T83E0 000:412.762 JLINK_GetFirmwareString(...)
T83E0 000:412.773 - 0.011ms 
T83E0 000:412.789 JLINK_GetHardwareVersion()
T83E0 000:412.801 - 0.011ms returns 10000
T83E0 000:412.813 JLINK_GetSN()
T83E0 000:412.825 - 0.011ms returns -1
T83E0 000:412.837 JLINK_GetOEMString(...)
T83E0 000:412.853 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T83E0 000:413.761 - 0.909ms returns 0x00
T83E0 000:413.784 JLINK_HasError()
T83E0 000:413.803 JLINK_SetSpeed(5000)
T83E0 000:413.868 - 0.066ms 
T83E0 000:414.128 JLINK_HasError()
T83E0 000:414.146 JLINK_SetResetType(JLINKARM_RESET_TYPE_NORMAL)
T83E0 000:414.157 - 0.011ms returns JLINKARM_RESET_TYPE_NORMAL
T83E0 000:414.169 JLINK_Reset()
T83E0 000:415.053   Found SW-DP with ID 0x6BA02477
T83E0 000:418.822   DPIDR: 0x6BA02477
T83E0 000:418.900   CoreSight SoC-400 or earlier
T83E0 000:418.917   Scanning AP map to find all available APs
T83E0 000:419.257   AP[0]: Stopped AP scan as end of AP map seems to be reached
T83E0 000:419.590   Iterating through AP map to find AHB-AP to use
T83E0 000:419.619   Attach to CPU failed. Executing connect under reset.
T83E0 000:442.565   DPIDR: 0x6BA02477
T83E0 000:442.597   CoreSight SoC-400 or earlier
T83E0 000:442.616   Scanning AP map to find all available APs
T83E0 000:442.993   AP[0]: Stopped AP scan as end of AP map seems to be reached
T83E0 000:443.249   Iterating through AP map to find AHB-AP to use
T83E0 000:443.273   Could not find core in Coresight setup
T83E0 005:555.060   
  ***** Error: 
T83E0 005:555.149   Communication timed out: Requested 20 bytes, received 0 bytes !
